server:
  port: 8889
  context-path: /base-web
  tomcat:
    uri-encoding: UTF-8
    basedir: /data/base-web/tomcat
    max-threads: 2000
    min-spare-threads: 10
    accesslog:
      pattern: common
      enabled: true
      directory: ../logs
      prefix: base_access_log
      suffix: .log
      request-attributes-enabled: true
      rename-on-rotate: true
logging:
  level:
    root: WARN
    com.bskj.nis: INFO
    org.springframework: WARN
    org.apache.shiro: WARN
    org.mybatis: WARN
    org.apache.ibatis: WARN
    com.alibaba.druid: WARN
    org.hibernate: WARN
    java.sql: WARN
    javax.sql: WARN
    # 保留重要业务日志
    com.bskj.nis.modules.sys.utils.LogUtils: INFO
    com.bskj.nis.modules.sys.service.SystemService: INFO
    com.bskj.nis.common.config: INFO
#swagger配置
swagger:
  scan:
    path: .*/sys/.*
spring:
  profiles:  
    active: prod
  # DevTools配置
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/webapp
      exclude: static/**,WEB-INF/lib/**
    livereload:
      enabled: true
      
  #数据库的配置
  datasource:
    driverClassName: oracle.jdbc.driver.OracleDriver
    type: com.zaxxer.hikari.HikariDataSource
    
    hikari:
      minimum-idle: 10
      maximum-pool-size: 100
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 'x' FROM DUAL
      pool-name: bskj-pool
  #springMvc的配置
  mvc:
     view:
       prefix: /WEB-INF/views/
       suffix: .jsp
     servlet:
       load-on-startup: 1
     maxUploadSize: 104857600
  http:
    multipart:
      maxFileSize: 200Mb
      maxRequestSize: 4000Mb  
  #spring-ehcache的配置
  cache:
    type: ehcache
    ehcache:
      config: classpath:ehcache.xml
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

#mybatis的配置
mybatis:
  config-location: classpath:/mybatis-config.xml
  mapper-locations: classpath:/common-mappings/**/*.xml,
                    classpath:/mappings/**/*.xml
  type-aliases-package: com.bskj.nis
#admin页面管理Path
adminPath: /a
frontPath: /f
urlSuffix: .html

task:
  allow: true
sys:
  name: base-web
  index: 
#分页配置
page:
  pageSize: 10
#文件上传的路径
userfiles:
  basedir: c:/nis

hasold:
 rsda: false
#初始密码以及重置密码设置
strong:
 isintercept: false  #是否强制拦截    true拦截           false只提示不拦截
 password: 1234

 # 密码规则详细配置
 rules:
  minLength: 6           # 密码最小长度
  requireDigit: true     # 是否要求包含数字
  requireLowercase: true # 是否要求包含小写字母
  requireUppercase: false # 是否要求包含大写字母 
  requireSpecial: false   # 是否要求包含特殊字符
imgPhoto:
 size:1024*30 #头像大小裁剪至30KB，会丢失精度
 
# Shiro Session 超时时间（毫秒）
sessionOutTime: 1800000


