#
# Licensed to <PERSON><PERSON><PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# <PERSON><PERSON><PERSON> licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

##
# Services Management Web UI Security
server.name=http://localhost:8080
server.prefix=${server.name}/cas

cas.securityContext.serviceProperties.service=${server.prefix}/services/j_acegi_cas_security_check
# Names of roles allowed to access the CAS service manager
cas.securityContext.serviceProperties.adminRoles=ROLE_ADMIN
cas.securityContext.casProcessingFilterEntryPoint.loginUrl=${server.prefix}/login
cas.securityContext.ticketValidator.casServerUrlPrefix=${server.prefix}
# IP address or CIDR subnet allowed to access the /status URI of CAS that exposes health check information
cas.securityContext.status.allowedSubnet=127.0.0.1


cas.themeResolver.defaultThemeName=cas-theme-default
cas.viewResolver.basename=default_views

##
# Unique CAS node name
# host.name is used to generate unique Service Ticket IDs and SAMLArtifacts.  This is usually set to the specific
# hostname of the machine running the CAS node, but it could be any label so long as it is unique in the cluster.
host.name=cas.bskeji.com

##
# Database flavors for Hibernate
#
# One of these is needed if you are storing Services or Tickets in an RDBMS via JPA.
#
# database.hibernate.dialect=org.hibernate.dialect.OracleDialect
# database.hibernate.dialect=org.hibernate.dialect.MySQLInnoDBDialect
# database.hibernate.dialect=org.hibernate.dialect.HSQLDialect

##
# CAS Logout Behavior
# WEB-INF/cas-servlet.xml
#
# Specify whether CAS should redirect to the specifyed service parameter on /logout requests
# cas.logout.followServiceRedirects=false

##
# Single Sign-On Session Timeouts - 护理管理平台优化配置
# Defaults sourced from WEB-INF/spring-configuration/ticketExpirationPolices.xml
#
# ==================== TGT (Ticket Granting Ticket) 配置优化 ====================
#
# TGT最大生存时间优化：从8小时(28800秒)调整为9小时(32400秒)
# 优化理由：
# 1. 覆盖医护人员完整班次(8小时) + 交班缓冲时间(1小时)
# 2. 避免交班期间因TGT过期导致的登录中断
# 3. 适应护理管理平台的实际工作场景
# 修改时间：2024-12-27
# 修改人员：系统管理员
tgt.maxTimeToLiveInSeconds=32400

# TGT空闲超时优化：从2小时(7200秒)调整为4小时(14400秒)
# 优化理由：
# 1. 覆盖长时间手术场景(3-4小时)
# 2. 覆盖ICU抢救等紧急情况(2-4小时)
# 3. 避免医护人员在关键操作期间被强制登出
# 4. 夜班期间相对安静时段的支持
# 安全考虑：4小时空闲后仍需重新认证，保证安全性
tgt.timeToKillInSeconds=14400

##
# Service Ticket Timeout - 护理管理平台网络环境优化
# Default sourced from WEB-INF/spring-configuration/ticketExpirationPolices.xml
#
# ST超时时间优化：从10秒调整为90秒
# 优化理由：
# 1. 适应医院内网环境和移动设备切换场景
# 2. 覆盖护士站到病房的移动办公需求
# 3. 预留系统间跳转和网络波动的时间缓冲
# 4. 平衡安全性和用户体验
# 注意：实际配置在ticketExpirationPolicies.xml中生效
# 网络环境	ST超时时间	使用次数	适用场景	安全级别
# 纯内网	30秒	3次	固定工作站，网络稳定	 高
# 标准内网	60秒	3次	偶有网络波动	 中高
# 当前配置	90秒	3次	移动办公，网络切换	 中
# 原始配置	10秒	10次	理想网络环境	 高
st.timeToKillInSeconds=30

##
# Single Logout Out Callbacks
# Default sourced from WEB-INF/spring-configuration/argumentExtractorsConfiguration.xml
#
# To turn off all back channel SLO requests set slo.disabled to true
# slo.callbacks.disabled=false

##
# Service Registry Periodic Reloading Scheduler
# Default sourced from WEB-INF/spring-configuration/applicationContext.xml
#
# Force a startup delay of 2 minutes.
# service.registry.quartz.reloader.startDelay=120000
# 
# Reload services every 2 minutes
# service.registry.quartz.reloader.repeatInterval=120000

##
# Log4j
# Default sourced from WEB-INF/spring-configuration/log4jConfiguration.xml:
#
# It is often time helpful to externalize log4j.xml to a system path to preserve settings between upgrades.
# e.g. log4j.config.location=/etc/cas/log4j.xml
# log4j.config.location=classpath:log4j.xml
#
# log4j refresh interval in millis
# log4j.refresh.interval=60000


