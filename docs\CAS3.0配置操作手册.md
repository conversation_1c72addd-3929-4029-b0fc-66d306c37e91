# CAS3.0配置操作手册

> **护理管理平台专用版本**  
> 系统管理员和运维人员标准化配置指南  
> 版本：v1.0 | 更新时间：2024-12-27

---

## 📋 目录

- [1. 配置概述](#1-配置概述)
- [2. 配置文件清单](#2-配置文件清单)
- [3. 前置准备工作](#3-前置准备工作)
- [4. 分步操作指南](#4-分步操作指南)
- [5. 配置验证方法](#5-配置验证方法)
- [6. 风险控制措施](#6-风险控制措施)
- [7. 故障排查指南](#7-故障排查指南)
- [8. 附录：配置模板](#8-附录配置模板)

---

## 1. 配置概述

### 🎯 配置目标
本次配置优化针对护理管理平台的业务特点，主要目标：
- **延长TGT生存时间**：从8小时调整为9小时，覆盖完整班次
- **优化空闲超时**：从2小时调整为4小时，支持长时间手术场景
- **调整ST参数**：超时90秒，使用3次，适应移动办公
- **优化缓存清理**：从83分钟调整为30分钟，提升内存效率

### ⚠️ 重要提醒
```
🚨 配置操作风险等级：中等
📅 建议操作时间：业务低峰期（22:00-06:00）
👥 操作人员要求：至少2人（主操作员+监督员）
⏱️ 预计操作时间：30-45分钟
🔄 回滚准备时间：5-10分钟
```

### 📊 配置参数对比表

| 配置项 | 优化前 | 优化后 | 业务价值 |
|--------|--------|--------|----------|
| TGT最大生存时间 | 28800秒(8小时) | **32400秒(9小时)** | 覆盖交班时间 |
| TGT空闲超时 | 7200秒(2小时) | **14400秒(4小时)** | 支持长手术 |
| ST超时时间 | 10秒 | **90秒** | 适应移动办公 |
| ST使用次数 | 10次 | **3次** | 提升安全性 |
| 缓存清理间隔 | 5000000毫秒(83分钟) | **1800000毫秒(30分钟)** | 优化内存使用 |

---

## 2. 配置文件清单

### 📁 核心配置文件

#### 2.1 主配置文件
```
文件名称：cas.properties
文件路径：WebContent/WEB-INF/cas.properties
重要性级别：🔴 高危险 - 影响整个CAS服务
修改内容：TGT和ST时间参数
备份要求：必须备份
```

#### 2.2 票据过期策略配置
```
文件名称：ticketExpirationPolicies.xml
文件路径：WebContent/WEB-INF/spring-configuration/ticketExpirationPolicies.xml
重要性级别：🔴 高危险 - 影响票据生命周期
修改内容：ST使用次数和超时策略
备份要求：必须备份
```

#### 2.3 票据注册表配置
```
文件名称：ticketRegistry.xml
文件路径：WebContent/WEB-INF/spring-configuration/ticketRegistry.xml
重要性级别：🟡 中危险 - 影响缓存清理
修改内容：缓存清理间隔
备份要求：必须备份
```

#### 2.4 认证配置文件（备份用）
```
文件名称：deployerConfigContext.xml
文件路径：WebContent/WEB-INF/deployerConfigContext.xml
重要性级别：🟡 中危险 - 认证核心配置
修改内容：无（仅备份）
备份要求：建议备份
```

### 📂 配置文件依赖关系
```
cas.properties (主配置)
    ↓ 参数引用
ticketExpirationPolicies.xml (策略配置)
    ↓ 服务依赖
ticketRegistry.xml (注册表配置)
    ↓ 认证集成
deployerConfigContext.xml (认证配置)
```

---

## 3. 前置准备工作

### 🔍 环境检查清单

#### 3.1 系统状态检查
```bash
# 检查CAS服务状态
systemctl status cas
# 或者
ps aux | grep cas

# 检查端口占用
netstat -tlnp | grep 9091

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

#### 3.2 服务依赖检查
```bash
# 检查数据库连接
telnet ************* 1521

# 检查Base3.0服务状态
curl -I http://192.168.0.57:8889/base-web

# 检查当前登录用户数
# 查看CAS管理界面或日志
```

### 💾 配置备份操作

#### 3.3 执行备份脚本
```bash
# 进入项目根目录
cd /path/to/cas3.0

# 执行备份脚本
./bat/backup-cas-config.bat

# 验证备份完成
ls -la config-backup/
```

#### 3.4 手动备份验证
```bash
# 创建备份目录
mkdir -p /backup/cas-config-$(date +%Y%m%d_%H%M%S)

# 备份关键配置文件
cp WebContent/WEB-INF/cas.properties /backup/cas-config-$(date +%Y%m%d_%H%M%S)/
cp WebContent/WEB-INF/spring-configuration/ticketExpirationPolicies.xml /backup/cas-config-$(date +%Y%m%d_%H%M%S)/
cp WebContent/WEB-INF/spring-configuration/ticketRegistry.xml /backup/cas-config-$(date +%Y%m%d_%H%M%S)/
cp WebContent/WEB-INF/deployerConfigContext.xml /backup/cas-config-$(date +%Y%m%d_%H%M%S)/

# 验证备份文件
ls -la /backup/cas-config-*/
```

### 📋 前置条件检查表

| 检查项目 | 状态 | 备注 |
|----------|------|------|
| CAS服务正常运行 | ☐ | 必须正常 |
| 数据库连接正常 | ☐ | 必须正常 |
| 磁盘空间充足(>1GB) | ☐ | 必须满足 |
| 内存使用率<80% | ☐ | 建议满足 |
| 配置文件已备份 | ☐ | 必须完成 |
| 回滚方案已准备 | ☐ | 必须完成 |
| 操作人员已到位 | ☐ | 必须满足 |
| 业务方已通知 | ☐ | 建议完成 |

---

## 4. 分步操作指南

### 🚀 操作流程总览
```
步骤1: 停止CAS服务 → 步骤2: 修改cas.properties → 
步骤3: 修改ticketExpirationPolicies.xml → 步骤4: 修改ticketRegistry.xml → 
步骤5: 启动CAS服务 → 步骤6: 验证配置
```

### 📝 详细操作步骤

#### 步骤1：停止CAS服务
```bash
# 停止CAS服务
systemctl stop cas
# 或者
/path/to/cas/bin/shutdown.sh

# 确认服务已停止
ps aux | grep cas
netstat -tlnp | grep 9091

# 等待30秒确保完全停止
sleep 30
```

**验证标准**：
- ✅ 进程列表中无CAS相关进程
- ✅ 9091端口无监听
- ✅ 日志显示正常关闭

#### 步骤2：修改cas.properties配置
```bash
# 编辑主配置文件
vi WebContent/WEB-INF/cas.properties
```

**具体修改内容**：
```properties
# 在文件末尾添加或修改以下配置

##
# Single Sign-On Session Timeouts - 护理管理平台优化配置
# 修改时间：2024-12-27
# 修改说明：针对医护人员工作特点进行优化

# TGT最大生存时间：9小时（覆盖8小时班次+1小时交班）
tgt.maxTimeToLiveInSeconds=32400

# TGT空闲超时：4小时（支持长手术和抢救场景）
tgt.timeToKillInSeconds=14400

# ST超时时间：90秒（适应移动办公和网络切换）
st.timeToKillInSeconds=90
```

**配置验证**：
```bash
# 检查配置语法
grep -E "^tgt\.|^st\." WebContent/WEB-INF/cas.properties

# 验证配置值
grep "tgt.maxTimeToLiveInSeconds=32400" WebContent/WEB-INF/cas.properties
grep "tgt.timeToKillInSeconds=14400" WebContent/WEB-INF/cas.properties
grep "st.timeToKillInSeconds=90" WebContent/WEB-INF/cas.properties
```

#### 步骤3：修改ticketExpirationPolicies.xml配置
```bash
# 编辑票据过期策略配置
vi WebContent/WEB-INF/spring-configuration/ticketExpirationPolicies.xml
```

**具体修改内容**：
找到以下配置块并替换：
```xml
<!-- 原配置 -->
<bean id="serviceTicketExpirationPolicy" 
      class="org.jasig.cas.ticket.support.MultiTimeUseOrTimeoutExpirationPolicy"
      c:numberOfUses="10" c:timeToKill="${st.timeToKillInSeconds:60}" c:timeUnit-ref="SECONDS"/>

<!-- 修改为 -->
<bean id="serviceTicketExpirationPolicy" 
      class="org.jasig.cas.ticket.support.MultiTimeUseOrTimeoutExpirationPolicy"
      c:numberOfUses="3" c:timeToKill="${st.timeToKillInSeconds:90}" c:timeUnit-ref="SECONDS"/>
```

找到以下配置块并替换：
```xml
<!-- 原配置 -->
<bean id="grantingTicketExpirationPolicy" 
      class="org.jasig.cas.ticket.support.TicketGrantingTicketExpirationPolicy"
      p:maxTimeToLiveInSeconds="${tgt.maxTimeToLiveInSeconds:28800}"
      p:timeToKillInSeconds="${tgt.timeToKillInSeconds:7200}"/>

<!-- 修改为 -->
<bean id="grantingTicketExpirationPolicy" 
      class="org.jasig.cas.ticket.support.TicketGrantingTicketExpirationPolicy"
      p:maxTimeToLiveInSeconds="${tgt.maxTimeToLiveInSeconds:32400}"
      p:timeToKillInSeconds="${tgt.timeToKillInSeconds:14400}"/>
```

**配置验证**：
```bash
# 检查XML语法
xmllint --noout WebContent/WEB-INF/spring-configuration/ticketExpirationPolicies.xml

# 验证ST配置
grep 'numberOfUses="3"' WebContent/WEB-INF/spring-configuration/ticketExpirationPolicies.xml
grep 'timeToKill="${st.timeToKillInSeconds:90}"' WebContent/WEB-INF/spring-configuration/ticketExpirationPolicies.xml

# 验证TGT配置
grep 'maxTimeToLiveInSeconds="${tgt.maxTimeToLiveInSeconds:32400}"' WebContent/WEB-INF/spring-configuration/ticketExpirationPolicies.xml
grep 'timeToKillInSeconds="${tgt.timeToKillInSeconds:14400}"' WebContent/WEB-INF/spring-configuration/ticketExpirationPolicies.xml
```

#### 步骤4：修改ticketRegistry.xml配置
```bash
# 编辑票据注册表配置
vi WebContent/WEB-INF/spring-configuration/ticketRegistry.xml
```

**具体修改内容**：
找到以下配置块并替换：
```xml
<!-- 原配置 -->
<bean id="triggerJobDetailTicketRegistryCleaner" 
      class="org.springframework.scheduling.quartz.SimpleTriggerBean"
      p:jobDetail-ref="jobDetailTicketRegistryCleaner"
      p:startDelay="20000"
      p:repeatInterval="5000000" />

<!-- 修改为 -->
<bean id="triggerJobDetailTicketRegistryCleaner" 
      class="org.springframework.scheduling.quartz.SimpleTriggerBean"
      p:jobDetail-ref="jobDetailTicketRegistryCleaner"
      p:startDelay="20000"
      p:repeatInterval="1800000" />
```

**配置验证**：
```bash
# 检查XML语法
xmllint --noout WebContent/WEB-INF/spring-configuration/ticketRegistry.xml

# 验证清理间隔配置
grep 'repeatInterval="1800000"' WebContent/WEB-INF/spring-configuration/ticketRegistry.xml
```

#### 步骤5：启动CAS服务
```bash
# 启动CAS服务
systemctl start cas
# 或者
/path/to/cas/bin/startup.sh

# 等待服务启动
sleep 60

# 检查服务状态
systemctl status cas
ps aux | grep cas
netstat -tlnp | grep 9091
```

**启动验证标准**：
- ✅ 服务进程正常运行
- ✅ 9091端口正常监听
- ✅ 日志无ERROR级别错误
- ✅ 登录页面可正常访问

#### 步骤6：配置验证
```bash
# 运行配置验证脚本
./bat/verify-cas-config.bat

# 手动验证关键配置
curl -I http://*************:9091/cas/login
```

---

## 5. 配置验证方法

### 🔍 功能验证测试

#### 5.1 基础功能测试
```bash
# 测试1：CAS登录页面访问
curl -s -o /dev/null -w "%{http_code}" http://*************:9091/cas/login
# 期望结果：200

# 测试2：CAS服务状态检查
curl -s http://*************:9091/cas/login | grep -i "username"
# 期望结果：包含用户名输入框

# 测试3：Base3.0重定向测试
curl -I http://192.168.0.57:8889/base-web/ 2>/dev/null | grep Location
# 期望结果：重定向到CAS登录页面
```

#### 5.2 登录流程测试
```
测试步骤：
1. 访问 http://192.168.0.57:8889/base-web/
2. 确认重定向到CAS登录页面
3. 输入测试用户凭据
4. 确认登录成功并重定向回base3.0
5. 验证用户会话正常

期望结果：
✅ 重定向流程正常
✅ 登录验证成功
✅ 会话创建正常
✅ 业务功能可用
```

### 📊 性能验证测试

#### 5.3 时间参数验证
```bash
# 验证TGT配置生效
# 方法：查看CAS日志中的TGT创建记录
tail -f /path/to/cas/logs/cas.log | grep "TGT"

# 验证ST配置生效
# 方法：查看ST创建和验证记录
tail -f /path/to/cas/logs/cas.log | grep "ST"

# 验证缓存清理配置
# 方法：观察清理任务执行频率
tail -f /path/to/cas/logs/cas.log | grep "clean"
```

#### 5.4 内存使用监控
```bash
# 监控CAS进程内存使用
ps aux | grep cas | awk '{print $6}'

# 监控系统整体内存
free -h

# 持续监控30分钟，观察内存变化趋势
for i in {1..30}; do
    echo "$(date): $(ps aux | grep cas | awk '{print $6}') KB"
    sleep 60
done
```

### ✅ 验证检查表

| 验证项目 | 验证方法 | 期望结果 | 实际结果 | 状态 |
|----------|----------|----------|----------|------|
| CAS服务启动 | systemctl status cas | active (running) | | ☐ |
| 登录页面访问 | curl HTTP状态码 | 200 | | ☐ |
| 配置文件语法 | xmllint检查 | 无错误 | | ☐ |
| TGT时间配置 | 日志验证 | 32400秒 | | ☐ |
| ST时间配置 | 日志验证 | 90秒 | | ☐ |
| 缓存清理间隔 | 日志验证 | 30分钟 | | ☐ |
| 登录流程测试 | 手动测试 | 成功登录 | | ☐ |
| Base3.0集成 | 访问测试 | 正常重定向 | | ☐ |
| 内存使用稳定 | 监控30分钟 | 无异常增长 | | ☐ |
| 错误日志检查 | 日志分析 | 无ERROR | | ☐ |

---

## 6. 风险控制措施

### ⚠️ 高风险操作识别

#### 6.1 关键风险点
```
🔴 高风险操作：
1. 修改cas.properties主配置文件
   风险：可能导致CAS服务无法启动
   
2. 修改ticketExpirationPolicies.xml
   风险：可能导致票据策略异常
   
3. 服务重启操作
   风险：影响在线用户使用

🟡 中风险操作：
1. 修改ticketRegistry.xml
   风险：影响缓存清理效率
   
2. 配置验证操作
   风险：可能暴露系统信息
```

#### 6.2 安全检查点
```
检查点1：配置修改前
- 确认备份完成
- 确认操作权限
- 确认业务影响范围

检查点2：配置修改中
- 逐步修改，分步验证
- 保持配置文件语法正确
- 记录每个修改步骤

检查点3：服务重启前
- 确认配置文件无语法错误
- 确认依赖服务正常
- 准备回滚方案

检查点4：服务重启后
- 确认服务正常启动
- 确认基础功能可用
- 确认无错误日志
```

### 🔄 完整回滚方案

#### 6.3 快速回滚步骤
```bash
# 紧急回滚脚本（5分钟内完成）

# 步骤1：停止CAS服务
systemctl stop cas

# 步骤2：恢复配置文件
./bat/restore-cas-config.bat
# 或手动恢复
cp /backup/cas-config-*/cas.properties WebContent/WEB-INF/
cp /backup/cas-config-*/ticketExpirationPolicies.xml WebContent/WEB-INF/spring-configuration/
cp /backup/cas-config-*/ticketRegistry.xml WebContent/WEB-INF/spring-configuration/

# 步骤3：启动CAS服务
systemctl start cas

# 步骤4：验证回滚成功
curl -I http://*************:9091/cas/login
```

#### 6.4 回滚触发条件
```
立即回滚条件：
- CAS服务无法启动
- 登录功能完全失效
- 出现严重错误日志
- 系统资源异常（内存/CPU）

考虑回滚条件：
- 用户投诉显著增加
- 性能明显下降
- 业务功能异常
- 监控指标异常
```

### 📞 应急联系流程
```
故障等级1（系统不可用）：
1. 立即执行回滚操作
2. 通知技术负责人：[电话]
3. 通知业务负责人：[电话]
4. 启动应急预案

故障等级2（功能异常）：
1. 分析问题原因
2. 尝试在线修复
3. 通知相关人员
4. 记录处理过程

故障等级3（性能问题）：
1. 监控系统状态
2. 收集性能数据
3. 制定优化方案
4. 安排维护窗口
```

---

## 7. 故障排查指南

### 🔧 常见问题及解决方案

#### 7.1 服务启动失败
```
问题现象：CAS服务无法启动

排查步骤：
1. 检查配置文件语法
   xmllint --noout WebContent/WEB-INF/spring-configuration/*.xml
   
2. 检查端口占用
   netstat -tlnp | grep 9091
   
3. 检查日志错误
   tail -f /path/to/cas/logs/cas.log
   
4. 检查Java进程
   ps aux | grep java

常见原因：
- 配置文件语法错误
- 端口被占用
- 内存不足
- 依赖服务异常

解决方案：
- 修复配置语法错误
- 释放被占用端口
- 增加内存分配
- 重启依赖服务
```

#### 7.2 登录功能异常
```
问题现象：用户无法正常登录

排查步骤：
1. 检查CAS登录页面
   curl -I http://*************:9091/cas/login
   
2. 检查数据库连接
   telnet ************* 1521
   
3. 检查认证日志
   tail -f /path/to/cas/logs/cas.log | grep "authentication"
   
4. 测试用户凭据
   # 使用已知正确的测试账号

常见原因：
- 数据库连接异常
- 用户凭据错误
- 认证配置问题
- 网络连接问题

解决方案：
- 修复数据库连接
- 验证用户账号状态
- 检查认证配置
- 排查网络问题
```

#### 7.3 性能问题排查
```
问题现象：系统响应缓慢

排查步骤：
1. 检查系统资源
   top
   free -h
   df -h
   
2. 检查CAS进程状态
   ps aux | grep cas
   
3. 检查网络连接
   netstat -an | grep 9091
   
4. 分析访问日志
   tail -f /path/to/cas/logs/access.log

常见原因：
- 内存不足
- CPU使用率过高
- 磁盘空间不足
- 网络延迟
- 数据库性能问题

解决方案：
- 增加系统资源
- 优化配置参数
- 清理磁盘空间
- 优化网络配置
- 调优数据库
```

### 📋 故障排查检查表

| 检查项目 | 检查命令 | 正常标准 | 当前状态 | 备注 |
|----------|----------|----------|----------|------|
| CAS服务状态 | systemctl status cas | active (running) | ☐ | |
| 端口监听 | netstat -tlnp \| grep 9091 | 有监听 | ☐ | |
| 进程状态 | ps aux \| grep cas | 进程存在 | ☐ | |
| 内存使用 | free -h | 可用内存>1GB | ☐ | |
| 磁盘空间 | df -h | 使用率<85% | ☐ | |
| 数据库连接 | telnet ************* 1521 | 连接成功 | ☐ | |
| 登录页面 | curl -I cas/login | HTTP 200 | ☐ | |
| 配置语法 | xmllint检查 | 无错误 | ☐ | |
| 错误日志 | grep ERROR logs/ | 无新错误 | ☐ | |
| Base3.0集成 | 访问base-web | 正常重定向 | ☐ | |

---

## 8. 附录：配置模板

### 📄 完整配置模板

#### 8.1 cas.properties配置模板
```properties
# CAS Server Context Configuration
server.name=http://*************:9091
server.prefix=${server.name}/cas
host.name=*************

# Database Configuration
database.hibernate.dialect=org.hibernate.dialect.Oracle10gDialect

# Single Sign-On Session Timeouts - 护理管理平台优化配置
# 修改时间：2024-12-27
# 修改说明：针对医护人员工作特点进行优化

# TGT最大生存时间：9小时（覆盖8小时班次+1小时交班）
tgt.maxTimeToLiveInSeconds=32400

# TGT空闲超时：4小时（支持长手术和抢救场景）
tgt.timeToKillInSeconds=14400

# ST超时时间：90秒（适应移动办公和网络切换）
st.timeToKillInSeconds=90

# Logging Configuration
log4j.rootLogger=INFO, cas
log4j.logger.org.jasig.cas=INFO
log4j.logger.org.springframework=WARN
log4j.logger.org.springframework.webflow=WARN
log4j.logger.org.springframework.web=WARN
```

#### 8.2 ticketExpirationPolicies.xml配置模板
```xml
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans.xsd
                           http://www.springframework.org/schema/util
                           http://www.springframework.org/schema/util/spring-util.xsd">

    <description>
        Configuration for ticket expiration policies.
        护理管理平台优化版本 - 2024-12-27
    </description>

    <!-- Expiration policies -->
    <util:constant id="SECONDS" static-field="java.util.concurrent.TimeUnit.SECONDS"/>
    
    <!-- Service Ticket过期策略 - 优化配置 -->
    <bean id="serviceTicketExpirationPolicy" 
          class="org.jasig.cas.ticket.support.MultiTimeUseOrTimeoutExpirationPolicy"
          c:numberOfUses="3" 
          c:timeToKill="${st.timeToKillInSeconds:90}" 
          c:timeUnit-ref="SECONDS"/>

    <!-- TGT过期策略 - 优化配置 -->
    <bean id="grantingTicketExpirationPolicy" 
          class="org.jasig.cas.ticket.support.TicketGrantingTicketExpirationPolicy"
          p:maxTimeToLiveInSeconds="${tgt.maxTimeToLiveInSeconds:32400}"
          p:timeToKillInSeconds="${tgt.timeToKillInSeconds:14400}"/>

</beans>
```

#### 8.3 ticketRegistry.xml配置模板
```xml
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans.xsd">

    <description>
        Configuration for the default TicketRegistry.
        护理管理平台优化版本 - 缓存清理策略优化
    </description>
       
    <!-- Ticket Registry -->
    <bean id="ticketRegistry" class="org.jasig.cas.ticket.registry.DefaultTicketRegistry" />
    
    <!-- Ticket Registry Cleaner - 优化配置 -->
    <bean id="ticketRegistryCleaner" 
          class="org.jasig.cas.ticket.registry.support.DefaultTicketRegistryCleaner"
          p:ticketRegistry-ref="ticketRegistry" />
    
    <bean id="jobDetailTicketRegistryCleaner" 
          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean"
          p:targetObject-ref="ticketRegistryCleaner"
          p:targetMethod="clean" />
    
    <!-- 缓存清理间隔优化：30分钟清理一次 -->
    <bean id="triggerJobDetailTicketRegistryCleaner" 
          class="org.springframework.scheduling.quartz.SimpleTriggerBean"
          p:jobDetail-ref="jobDetailTicketRegistryCleaner"
          p:startDelay="20000"
          p:repeatInterval="1800000" />

    <!-- Scheduler -->
    <bean id="scheduler" class="org.springframework.scheduling.quartz.SchedulerFactoryBean"
          p:triggers-ref="triggersList" />

    <util:list id="triggersList">
        <ref bean="triggerJobDetailTicketRegistryCleaner" />
    </util:list>

</beans>
```

### 📋 配置检查清单模板

#### 8.4 操作前检查清单
```
□ 确认操作时间（建议22:00-06:00）
□ 确认操作人员到位（主操作员+监督员）
□ 确认CAS服务当前状态正常
□ 确认数据库连接正常
□ 确认Base3.0服务正常
□ 确认磁盘空间充足（>1GB）
□ 确认内存使用率正常（<80%）
□ 确认配置文件备份完成
□ 确认回滚方案准备就绪
□ 确认业务方已收到通知
□ 确认应急联系人在线
□ 确认监控工具正常运行
```

#### 8.5 操作后验证清单
```
□ CAS服务正常启动
□ 9091端口正常监听
□ 登录页面可正常访问
□ 配置文件语法检查通过
□ TGT时间参数配置生效
□ ST时间参数配置生效
□ 缓存清理间隔配置生效
□ 登录流程测试通过
□ Base3.0集成测试通过
□ 系统日志无ERROR级别错误
□ 内存使用情况正常
□ 性能指标无异常
□ 用户反馈收集正常
□ 配置变更记录完成
```

---

## 📞 技术支持

### 🆘 联系方式
- **技术负责人**：[姓名] - [电话] - [邮箱]
- **系统管理员**：[姓名] - [电话] - [邮箱]
- **应急联系人**：[姓名] - [电话] - [邮箱]

### 📚 相关文档
- [CAS单点登录系统技术架构学习指南](./CAS单点登录系统技术架构学习指南.md)
- [配置优化记录](./upgrade/cas-timeout-optimization-2024-12-27.md)
- [故障排查手册](./troubleshooting/cas-troubleshooting-guide.md)

---

**文档版本**：v1.0  
**创建时间**：2024-12-27  
**适用环境**：护理管理平台CAS3.0生产环境  
**维护团队**：系统运维组

*本手册为护理管理平台CAS3.0系统专用配置操作指南，请严格按照步骤执行，确保系统安全稳定运行。*
