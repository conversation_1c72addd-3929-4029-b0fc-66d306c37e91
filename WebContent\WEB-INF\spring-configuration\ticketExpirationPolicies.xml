<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to <PERSON>asig under one or more contributor license
    agreements. See the NOTICE file distributed with this work
    for additional information regarding copyright ownership.
    Jasig licenses this file to you under the Apache License,
    Version 2.0 (the "License"); you may not use this file
    except in compliance with the License.  You may obtain a
    copy of the License at the following location:

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans.xsd
                           http://www.springframework.org/schema/util
                           http://www.springframework.org/schema/util/spring-util.xsd">
    <description>
        Assignment of expiration policies for the different tickets generated by CAS including ticket granting ticket
        (TGT), service ticket (ST), proxy granting ticket (PGT), and proxy ticket (PT).
        These expiration policies determine how long the ticket they are assigned to can be used and even how often they
        can be used before becoming expired / invalid.
    </description>

    <!-- ==================== 护理管理平台票据过期策略优化配置 ==================== -->
    <!-- 修改时间：2024-12-27 -->
    <!-- 修改说明：针对医护人员工作特点进行的专项优化 -->

    <!-- Expiration policies -->
    <util:constant id="SECONDS" static-field="java.util.concurrent.TimeUnit.SECONDS"/>

    <!-- Service Ticket 过期策略优化 -->
    <!--
    优化内容：
    1. 使用次数：从10次降低到5次，提升安全性
    2. 超时时间：从60秒调整为90秒，适应移动办公场景

    业务场景考虑：
    - 护士站到病房的移动切换
    - 医院WiFi网络可能的延迟
    - 系统间跳转的时间需求
    - 移动设备的网络切换时间

    安全考虑：
    - 3次使用限制防止票据被恶意重复使用
    - 90秒超时平衡了便利性和安全性
    -->
    <bean id="serviceTicketExpirationPolicy" class="org.jasig.cas.ticket.support.MultiTimeUseOrTimeoutExpirationPolicy"
          c:numberOfUses="5" c:timeToKill="${st.timeToKillInSeconds:90}" c:timeUnit-ref="SECONDS"/>

    <!-- Ticket Granting Ticket 过期策略优化 -->
    <!--
    优化内容：
    1. 最大生存时间：从8小时(28800秒)调整为9小时(32400秒)
    2. 空闲超时：从2小时(7200秒)调整为4小时(14400秒)

    医护工作场景适配：
    - 覆盖完整的8小时工作班次 + 1小时交班时间
    - 支持长时间手术(3-4小时)和ICU抢救场景
    - 适应夜班相对安静时段的工作模式
    - 减少医护人员在关键操作时的登录中断

    安全平衡：
    - 9小时绝对超时确保不会无限期保持登录
    - 4小时空闲超时在便利性和安全性间取得平衡
    - 超过4小时无操作仍需重新认证
    -->
    <bean id="grantingTicketExpirationPolicy" class="org.jasig.cas.ticket.support.TicketGrantingTicketExpirationPolicy"
          p:maxTimeToLiveInSeconds="${tgt.maxTimeToLiveInSeconds:32400}"
          p:timeToKillInSeconds="${tgt.timeToKillInSeconds:14400}"/>
<!--     <bean id="grantingTicketExpirationPolicy" class="org.jasig.cas.ticket.support.NeverExpiresExpirationPolicy" />
 --></beans>